const path = require('path');

module.exports = {
  extract: {
    // 美杜莎应用名
    name: 'j-dingtalk-web',
    // 目标源码路径
    sourcePath: 'src',
    // 目标文件类型
    // 注：推荐使用 ts 类型，其包含 js、jsx、ts 和 tsx
    fileType: 'ts',
    prettier: false,
    // 原文语言
    sourceLang: 'zh-CN',
    // 钉钉翻译大模型配置（可选、数组）
    dingTalkTranslateOptions: [
      {
        // 目标语言
        targetLang: 'en-US',
        // 目标语言保留词
        reservedWords: [
          {
            key: '钉钉',
            value: 'DingTalk',
          },
          {
            key: '让进步发生',
            value: 'Make It Happen',
          },
        ],
      },
    ],
    // 是否上传到美杜莎
    isNeedUploadCopyToMedusa: true,
    // 是否保存提取日志
    saveExtractLog: true,
    // 排除文件
    exclude: (path) => {
      return path.includes('node_modules')
        || path.includes('\\.umi\\')
        || path.includes('idl')
        || path.includes('data');
    },
    // i18n宏配置
    macro: {
      // 资源路径
      path: 'src/i18n',
      // 宏引入
      import: "import { i18next } from '@ali/dingtalk-i18n'",
      // 宏函数
      method: "i18next.t('$key$')",
      // 自定义占位符
      placeholder: (variable) => {
        return `{{${variable}}}`;
      },
      // key分隔符
      // 注：默认 key 生成方式的分隔符，若资源文件为 js 文件，不支持用 . 来分隔
      keySplitter: '_',
      dependencies: ['@ali/dingtalk-i18n'],
      keyGenerator: (copy, filePath, config) => {
        function toRelativePath(absolutePath, cwd, splitter) {
          return absolutePath.replace(cwd + splitter, '');
        }

        const enCopy = copy['en-US'];
        const keySplitter = config.macro.keySplitter || DEFAULT_KEY_SPLITTER;
        // generate world part
        const words = enCopy.split(/[^a-zA-Z]/i);
        const namedWords = words
          .filter((w) => /^[\w|\d]+$/i.test(w))
          .map((w) => w.toLowerCase().replace(/(?:^|\s)\S/g, (a) => a.toUpperCase()));

        if (namedWords.length === 0) {
          return '';
        }

        // generate path part
        const { name } = config;
        const isWin = process.platform === 'win32';
        const splitter = isWin ? '\\' : '/';
        const relativeFilePath = toRelativePath(filePath, config.cwd, splitter);
        const parsedPath = path.parse(relativeFilePath);
        const dirPart = parsedPath.dir.split(splitter).filter((p) => !!p);
        dirPart.shift(); // remove src prefix
        let keyArray = [name];
        keyArray = keyArray.concat(dirPart); // get the last two

        if (parsedPath.name && parsedPath.name !== 'index') keyArray.push(parsedPath.name);
        const filePathKey = keyArray.join(keySplitter);

        return `${filePathKey}${keySplitter}${namedWords.slice(0, 5).join('')}`;
      },

      // 文案匹配规则（高级功能）
      matchCopy: (text, path) => {
        // 避免提取 console.log 中的中文字符
        const isConsoleLog = /^console\.log\(/gi.test(path.parentPath.toString());
        return /[^\x00-\xff]/.test(text) && !isConsoleLog;
      },
    },
    // Hook 函数（高级功能）
    hook: {
      beforeExtract: (sourceAST, absoluteFilePath, config) => {
        // console.log('文案提取之前');
        return sourceAST;
      },
      afterExtract: (injectedAST, keyMap, absoluteFilePath, config) => {
        // console.log('文案提取之后');
        return injectedAST;
      },
      beforeTranslate: (copywriting, config) => {
        // console.log('文案翻译之前');
        return copywriting;
      },
      afterTranslate: (copywriting, config) => {
        // console.log('文案翻译之后');
        return copywriting;
      },
    },
    babel: {
      allowImportExportEverywhere: true,
      decoratorsBeforeExport: true,
      prettier: false,
      plugins: [
        'asyncGenerators',
        'classProperties',
        'decorators-legacy',
        'doExpressions',
        'exportExtensions',
        'exportDefaultFrom',
        'typescript',
        'functionSent',
        'functionBind',
        'jsx',
        'objectRestSpread',
        'dynamicImport',
        'numericSeparator',
        'optionalChaining',
        'optionalCatchBinding',
      ],
    },
  },
};
