import { i18next } from '@ali/dingtalk-i18n';

/**
 * 格式化日期为标准格式（YYYY-MM-DD HH:mm:ss）
 * <AUTHOR>
 * @param {Date} date - 日期对象
 * @returns {string} 格式化后的日期字符串
 */
export function formatCompletionTime(date?: Date): string {
  if (!date) {
    return '';
  }

  const year = date.getFullYear();
  const month = padZero(date.getMonth() + 1);
  const day = padZero(date.getDate());
  const hours = padZero(date.getHours());
  const minutes = padZero(date.getMinutes());
  const seconds = padZero(date.getSeconds());

  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
}

/**
 * 数字补零（小于10的数字前面补0）
 * <AUTHOR>
 * @param {number} num - 需要补零的数字
 * @returns {string} 补零后的字符串
 */
export function padZero(num: number): string {
  return num < 10 ? `0${num}` : `${num}`;
}

// 格式化日期时间为相对时间（如"一个月前"，"一天前"，"一个小时前"，"一分钟前"等）
export const formatDateTime = (dateTime: number): string => {
  // 获取当前时间戳（毫秒）
  const now = Date.now();
  // 计算时间差（毫秒）
  const diff = now - dateTime;

  // 转换为秒
  const seconds = Math.floor(diff / 1000);
  // 转换为分钟
  const minutes = Math.floor(seconds / 60);
  // 转换为小时
  const hours = Math.floor(minutes / 60);
  // 转换为天
  const days = Math.floor(hours / 24);
  // 转换为月（按30天计算）
  const months = Math.floor(days / 30);
  // 转换为年
  const years = Math.floor(months / 12);

  // 根据时间差返回相应的文本
  if (years > 0) {
    return years === 1
      ? i18next.t('j-agent-web_utils_util_AYearAgo')
      : i18next.t('j-agent-web_utils_util_YearsYearsAgo', { years });
  } else if (months > 0) {
    return months === 1
      ? i18next.t('j-agent-web_utils_util_AMonthAgo')
      : i18next.t('j-agent-web_utils_util_MonthsMonthsAgo', { months });
  } else if (days > 0) {
    return days === 1
      ? i18next.t('j-agent-web_utils_util_ADayAgo')
      : i18next.t('j-agent-web_utils_util_DaysDaysAgo', { days });
  } else if (hours > 0) {
    return hours === 1
      ? i18next.t('j-agent-web_utils_util_AnHourAgo')
      : i18next.t('j-agent-web_utils_util_HoursHoursAgo', { hours });
  } else if (minutes > 0) {
    return minutes === 1
      ? i18next.t('j-agent-web_utils_util_OneMinuteAgo')
      : i18next.t('j-agent-web_utils_util_MinutesMinutesAgo', { minutes });
  } else {
    return seconds <= 5
      ? i18next.t('j-agent-web_utils_util_JustNow')
      : i18next.t('j-agent-web_utils_util_SecondsSecondsAgo', { seconds });
  }
};

/**
 * @desc 获取URL中的指定参数，优先获取浏览器路由参数，如果浏览器路由参数未获得指定值则尝试获取hash路由参数。
 * <AUTHOR>
 * @param {string} [key] 指定需要获取参数的属性名，可选，如果未传则返回全部参数
 * @return {string | Record<string, string>} URL中的指定参数
 *
 * @example
 * // 获取指定参数
 * getUrlParam('formUuid');
 */
export function getUrlParam(): Record<string, string>;
export function getUrlParam(key: string): string;
export function getUrlParam(key?: string): string | Record<string, string> {
  const { search } = window.location;
  const arr = !search ? [] : search.substring(1).split('&');
  const param: Record<string, any> = {};
  arr.forEach((item) => {
    const kv = item.split('=');
    if (kv && kv.length === 2) {
      param[kv[0]] = kv[1];
    }
  });

  // 当前param[key] 不存在的时候取hash 里面的
  if (!key || !param[key]) {
    const { hash } = window.location;
    const hashArr = hash && hash.includes('?') ? hash.split('?')[1].split('&') : [];
    hashArr.forEach((item) => {
      const kv = item.split('=');
      if (kv && kv.length === 2) {
        param[kv[0]] = kv[1];
      }
    });
  }

  if (key) {
    return param[key] || '';
  }

  return param;
}

/**
 * @desc 获取URL中的指定参数，并解码
 * <AUTHOR>
 * @param {string} [key] 指定需要获取参数的属性名，可选，如果未传则返回全部参数
 * @return {string | Record<string, string>} URL中的指定参数
 */
export function getDecodedUrlParam(): Record<string, string>;
export function getDecodedUrlParam(key: string): string;
export function getDecodedUrlParam(key?: string): string | Record<string, string> {
  const param = getUrlParam(key);
  if (typeof param === 'string') {
    return decodeURIComponent(param);
  }
  return param;
}

/**
 * @desc 获取页面参数
 * <AUTHOR>
 * @param {string} [key] 指定需要获取参数的属性名，可选，如果未传则返回全部参数
 * @return {string | Record<string, string>} URL中的指定参数
 */
export function getPageConfig(): Record<string, string>;
export function getPageConfig(key: string): string;
export function getPageConfig(key?: string): string | Record<string, string> {
  const config = { ...(window.pageConfig || {}) };
  if (key) {
    return decodeURIComponent(config[key] || '');
  }
  return config || {};
}

/**
 * @desc 格式化数值，支持万、亿单位，保留1位小数，支持国际化
 * <AUTHOR>
 * @param {number | string} value 需要格式化的数值
 * @return {string} 格式化后的字符串
 *
 * @example
 * formatNumber(1234) => '1234'
 * formatNumber(12345) => '1.2万' (中文) / '1.2K' (英文) / '1.2万' (日文)
 * formatNumber(123456789) => '1.2亿' (中文) / '1.2M' (英文) / '1.2億' (日文)
 */
export function formatNumber(value: number | string): string {
  const num = typeof value === 'string' ? parseFloat(value) : value;

  // Handle invalid numbers
  if (isNaN(num) || num === null || num === undefined) {
    return '0';
  }

  // Less than 10,000 - show original number
  if (num < 10000) {
    return num.toString();
  }

  // 10,000 to 99,999,999 - show in 万/K/万 (10 thousands)
  if (num < 100000000) {
    const wan = num / 10000;
    const unit = i18next.t('j-dingtalk-web_utils_util_NumberUnitTenThousand');
    return `${wan.toFixed(1)}${unit}`;
  }

  // 100,000,000 and above - show in 亿/M/億 (100 millions)
  const yi = num / 100000000;
  const unit = i18next.t('j-dingtalk-web_utils_util_NumberUnitHundredMillion');
  return `${yi.toFixed(1)}${unit}`;
}
