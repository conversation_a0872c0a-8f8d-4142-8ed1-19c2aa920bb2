.premium-store {
  min-height: 100vh;

  // Store Header Section
  .store-header {
    padding: 16px;
    display: flex;
    justify-content: flex-start;
    align-items: flex-start;
    gap: 8px;

    .ranking-badge {
      width: 18px;
      font-size: 0;
      margin-top: 3px;
      .ranking-number {
        width: 18px;
        height: 24px;
      }
    }

    .store-info {
      display: flex;
      align-items: flex-start;
      flex-direction: column;
      gap: 4px;
      flex: 1;

      .store-name-container {
        display: flex;
        align-items: center;
        justify-content: space-between;
      }

      .store-name {
        font-size: 16px;
        font-weight: 500;
        color: var(--common_level1_base_color);
        flex: 1;
        max-width: 260px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        line-height: 24px;
        margin-right: 4px;
      }

      .store-tag {
        display: inline-block;
        color: var(--common_level2_base_color);
        font-size: 10px;
        line-height: 12px;
        padding: 3px 6px;
        border-radius: 4px;
        border: 0.5px solid var(--common_line_hard_color);
        margin-right: 4px;
      }

      .store-arrow {
        display: flex;
        align-items: center;
        font-size: 14px;
        color: var(--common_level2_base_color);
      }

      .store-rating {
        display: flex;
        align-items: center;
        gap: 6px;

        .rating-label {
          font-size: 14px;
          line-height: 22px;
          color: var(--common_level2_base_color);
        }

        .stars-container {
          display: flex;
          align-items: center;
          .store-rates {
            --star-size: 16px !important;
            --active-color: #FF0E53 !important;
          }
        }
      }
    }
  }

  // Rating Details Section
  .rating-details {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: var(--common_bg_z0_color);
    border-radius: 8px;
    padding: 8px 0;
    margin: 0 16px;

    .rating-item {
      display: flex;
      align-items: center;
      justify-content: center;
      flex: 1;

      .rating-category {
        font-size: 12px;
        font-weight: 400;
        color: var(--common_level2_base_color);
      }

      .rating-value {
        font-size: 18px;
        font-weight: 500;
        color: #FF0E53;
      }
    }

    .rating-line {
      width: 0.5px;
      height: 16px;
      background-color: var(--common_line_hard_color);
      margin: 0 8px;
    }
  }

  // Merchant Products
  .product-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 24px 16px;
    margin: 0 16px;
    padding: 24px 0;

    .product-item {
      width: 100%;
      box-sizing: border-box;
      cursor: pointer;

      .product-image-container {
        position: relative;
        margin-bottom: 8px;
        font-size: 0;

        .product-image {
          height: 213px;
          font-size: 0;
          border-radius: 8px;
          overflow: hidden;
          object-fit: cover;
        }

        .product-actions {
          position: absolute;
          bottom: 8px;
          left: 8px;
          right: 8px;
          display: flex;
          justify-content: flex-end;
          align-items: center;

          &.none {
            display: none;
          }

          .similar-button {
            border: none;
            border-radius: 16px;
            color: #ffffff;
            background-color: rgba(0, 0, 0, 0.6);
            font-size: 12px;
            padding: 6px 12px;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 4px;
            .similar-icon {
              font-size: 18px;
            }
          }
        }
      }

      .product-info {
        .product-title {
          font-size: 14px;
          font-weight: 500;
          color: var(--common_level1_base_color);
          line-height: 22px;
          margin-bottom: 8px;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          overflow: hidden;
        }

        .product-price {
          font-size: 24px;
          color: #FF0E53;
          font-weight: 600;
          line-height: 24px;
          margin-bottom: 4px;
          span {
            font-size: 14px;
          }
        }

        .product-sales {
          display: inline-block;
          font-size: 10px;
          line-height: 12px;
          border-radius: 4px;
          padding: 2.5px 4px;
          color: var(--common_level2_base_color);
          border: 0.5px solid var(--common_level4_base_color);
        }
      }
    }
  }

  // Loading more indicator styles
  .loading-more-container {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 20px;
    margin-top: 16px;

    .loading-more-text {
      font-size: 14px;
      color: var(--common_level2_base_color);
      display: flex;
      align-items: center;
      gap: 8px;

      &::after {
        content: '';
        width: 16px;
        height: 16px;
        border: 2px solid var(--common_level3_base_color);
        border-top: 2px solid var(--common_level1_base_color);
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }
    }
  }

  .no-more-data-container {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 20px;
    margin-top: 16px;

    .no-more-data-text {
      font-size: 14px;
      color: var(--common_level3_base_color);
    }
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
}
