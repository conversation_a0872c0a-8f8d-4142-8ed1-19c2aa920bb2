// init react component
import { useEffect, useState } from 'react';
import i18next from 'i18next';
import { sendUT } from '@/utils/trace';
import { getUrlParam, getDecodedUrlParam } from '@/utils/util';
import { openLink, setPageTitle } from '@/utils/jsapi';
import OptimizedImage from '@/components/OptimizedImage';
import Loading from '@/components/Loading';
import {
  SearchOutlined,
  // RightArrowOutlined,
} from '@ali/ding-icons';
import { Rate, Toast } from 'dingtalk-design-mobile';
import { queryGoodShoppExpertDetail } from '@/apis/production';
import { getLwpSourceUrl } from '@/utils/env';

import './index.less';

interface StoreInfo {
  shopName: string;
  shopNameJa: string;
  shopId: string;
  categoryName: string;
  consultScore: number;
  logisticsScore?: number;
  qualityScore?: number;
  serviceScore?: number;
  fansNum?: string;
  positiveRate?: string;
}

const PremiumStore = () => {
  // Get URL parameters, ensure type is Record<string, string>
  const params = getUrlParam() as Record<string, string>;
  const { platform, agentCode, shopId } = params;
  const bizId = getDecodedUrlParam('bizId');
  const [loading, setLoading] = useState<boolean>(true);
  const [storeInfo, setStoreInfo] = useState<StoreInfo>({
    shopName: '',
    shopNameJa: '',
    shopId: '',
    categoryName: '',
    consultScore: 0,
    logisticsScore: 0,
    qualityScore: 0,
    serviceScore: 0,
    fansNum: '',
    positiveRate: '',
  });
  const [products, setProducts] = useState<any[]>([]);

  // Pagination states
  const [displayCount, setDisplayCount] = useState<number>(20);
  const [isLoadingMore, setIsLoadingMore] = useState<boolean>(false);

  useEffect(() => {
    setPageTitle(i18next.t('j-dingtalk-web_pages_premiumStore_StoreDetails'));

    sendUT('premium_store_page_view', {});

    initData();
  }, []);

  // Scroll event listener for lazy loading
  useEffect(() => {
    const handleScroll = () => {
      if (isLoadingMore) return;

      const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
      const windowHeight = window.innerHeight;
      const documentHeight = document.documentElement.scrollHeight;

      // Load more when user scrolls to 200px from bottom
      if (scrollTop + windowHeight >= documentHeight - 200) {
        loadMoreProducts();
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, [displayCount, products, isLoadingMore]);

  const initData = async () => {
    try {
      setLoading(true);
      const res = await queryGoodShoppExpertDetail({
        platform,
        agentCode,
        bizId,
        code: shopId,
      });

      if (!res?.shop1688Response && !res?.shopTaobaoResponse) {
        Toast.fail({
          content: i18next.t('j-dingtalk-web_pages_premiumStore_FailedToLoadStoreData'),
          duration: 3,
          position: 'top',
          maskClickable: true,
        });
        return;
      }

      if (res?.shop1688Response) {
        setStoreInfo(res?.shop1688Response || {});
        setProducts(res?.shop1688Response?.productList || []);
      } else if (res?.shopTaobaoResponse) {
        setStoreInfo(res?.shopTaobaoResponse || {});
        setProducts(res?.shopTaobaoResponse?.productList || []);
      }
    } catch (error) {
      Toast.fail({
        content: i18next.t('j-dingtalk-web_pages_premiumStore_FailedToLoadStoreData'),
        duration: 3,
        position: 'top',
        maskClickable: true,
      });
    } finally {
      setLoading(false);
    }
  };

  // const handleProductClick = (product: any) => {
  //   sendUT('premium_store_product_click', { productId: product.productId });
  //   openLink(product.detailUrl, true);
  // };

  const handleSimilarProduct = (product: any) => {
    const { dspcCode, detailHtml } = product;
    sendUT('premium_store_similar_product_click', { dspcCode });

    if (platform === 'taobao_shop') {
      if (!dspcCode) {
        return;
      }

      const sourceUrl = getLwpSourceUrl(dspcCode);
      openLink(sourceUrl);
    } else if (platform === 'ali1688_shop') {
      if (!detailHtml) {
        return;
      }

      openLink(detailHtml, true);
    }
  };

  // Load more products function
  const loadMoreProducts = () => {
    if (isLoadingMore) return;

    // Check if all data is already loaded
    const currentCount = displayCount;
    const totalItems = products.length;
    if (currentCount >= totalItems) {
      return; // All data is already loaded, no need to load more
    }

    setIsLoadingMore(true);

    // Simulate loading delay
    setTimeout(() => {
      const newCount = Math.min(currentCount + 20, totalItems);
      setDisplayCount(newCount);
      setIsLoadingMore(false);
    }, 500);
  };

  // Show loading while data is being fetched
  if (loading) {
    return <Loading />;
  }

  return (
    <div className="premium-store">
      {/* Store Header */}
      <div className="store-header">
        <div className="ranking-badge">
          <img
            src="https://img.alicdn.com/imgextra/i2/O1CN01CgXIiB26RsXEv7HLk_!!6000000007659-2-tps-72-96.png"
            alt="merchant-badge"
            className="ranking-number"
            width={18}
            height={24}
          />

        </div>
        <div className="store-info">
          <div className="store-name-container">
            <div className="store-name">{storeInfo?.shopNameJa || storeInfo?.shopName}</div>
            <div className="store-tag">{storeInfo?.categoryName}</div>
          </div>
          {storeInfo?.serviceScore !== undefined &&
          <div className="store-rating">
            <span className="rating-label">{i18next.t('j-dingtalk-web_pages_premiumStore_ComprehensiveEvaluation')}</span>
            <div className="stars-container">
              <Rate
                className="store-rates"
                readOnly
                value={storeInfo?.serviceScore}
                allowHalf
              />

            </div>
          </div>
          }
        </div>
      </div>

      {/* Rating Details */}
      <div className="rating-details">
        {storeInfo?.qualityScore !== undefined &&
        <>
          <div className="rating-item">
            <span className="rating-category">{i18next.t('j-dingtalk-web_pages_premiumStore_ProductQuality')}</span>
            <span className="rating-value">{storeInfo.qualityScore}</span>
          </div>
          {(storeInfo?.logisticsScore !== undefined || storeInfo?.consultScore !== undefined) &&
          <div className="rating-line" />
          }
        </>
        }
        {storeInfo?.logisticsScore !== undefined &&
        <>
          <div className="rating-item">
            <span className="rating-category">{i18next.t('j-dingtalk-web_pages_premiumStore_DeliverySpeed')}</span>
            <span className="rating-value">{storeInfo.logisticsScore}</span>
          </div>
          {storeInfo?.consultScore !== undefined &&
          <div className="rating-line" />
          }
        </>
        }
        {storeInfo?.consultScore !== undefined &&
        <div className="rating-item">
          <span className="rating-category">{i18next.t('j-dingtalk-web_pages_premiumStore_ConsultingServices')}</span>
          <span className="rating-value">{storeInfo.consultScore}</span>
        </div>
        }
        {storeInfo?.fansNum !== undefined &&
        <>
          <div className="rating-item">
            <span className="rating-category">{i18next.t('j-dingtalk-web_pages_premiumStore_NumberOfFans')}</span>
            <span className="rating-value">{storeInfo.fansNum}</span>
          </div>
          {storeInfo?.fansNum !== undefined &&
          <div className="rating-line" />
          }
        </>
        }
        {storeInfo?.positiveRate !== undefined &&
        <div className="rating-item">
          <span className="rating-category">{i18next.t('j-dingtalk-web_pages_premiumStore_PositiveRate')}</span>
          <span className="rating-value">{storeInfo.positiveRate}</span>
        </div>
        }
      </div>

      {/* Merchant Products */}
      <div className="product-grid">
        {products?.slice(0, displayCount).map((product) =>

          (
            <div
              key={product.productId}
              className="product-item"
              onClick={(e) => {
                e.stopPropagation();
                handleSimilarProduct(product);
              }}
            >

              <div className="product-image-container">
                <OptimizedImage
                  src={product.primaryImage}
                  alt={product.titleJp || product.title}
                  className="product-image"
                  width="100%"
                  height={213}
                  // fit="contain"
                  lazy
                  progressive
                  quality={90}
                />

                <div className={`product-actions ${platform === 'ali1688_shop' ? 'none' : ''}`}>
                  <button
                    className="similar-button"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleSimilarProduct(product);
                    }}
                  >

                    <SearchOutlined className="similar-icon" />{i18next.t('j-dingtalk-web_pages_premiumStore_SimilarProducts')}
                  </button>
                </div>
              </div>
              <div className="product-info">
                <h4 className="product-title">{product.titleJp || product.title}</h4>
                <div className="product-price">
                  {product.salePrice}
                  <span>円</span>
                </div>
                {
                  product?.monthSoldDisplay ?
                    <div className="product-sales">{i18next.t('j-dingtalk-web_pages_premiumStore_MonthlySalesProductmonthsolddisplayPieces', { productMonthSoldDisplay: product.monthSoldDisplay })}</div> :
                    null
                }
              </div>
            </div>
          ))}
      </div>

      {/* Loading more indicator */}
      {isLoadingMore &&
      <div className="loading-more-container">
        <div className="loading-more-text">
          {i18next.t('j-dingtalk-web_components_Loading_Loading')}...
        </div>
      </div>
      }

      {/* Show "No more data" when all items are loaded */}
      {!isLoadingMore && displayCount >= products.length && products.length > 20 &&
      <div className="no-more-data-container">
        <div className="no-more-data-text">
          {i18next.t('j-dingtalk-web_pages_productOriginV2_components_ProductPage_NoMore')}
        </div>
      </div>
      }
    </div>);
};

export default PremiumStore;
