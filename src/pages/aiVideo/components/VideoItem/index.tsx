import { i18next } from '@ali/dingtalk-i18n';
import React, { useState, useRef, useEffect } from 'react';
import { Toast, Modal } from 'dingtalk-design-mobile';
import {
  LikeOutlined,
  LikeFilled,
  DislikeOutlined,
  DislikeFilled,
  RefreshOutlined,
  DownloadAndSaveOutlined,
  PlayOutlined,
  PauseFilled,
  PlayerFullscreenLOutlined,
  DownArrowOutlined,
  DeleteOutlined } from
  '@ali/ding-icons';
import { isMobileDevice } from '@/utils/jsapi';
import { formatCompletionTime } from '@/utils/util';
import { downloadVideo, downloadGif } from '@/utils/download';
import { generateGif, rateVideo, checkGifStatus, removeVideo, reGenerateVideo } from '@/apis';
import './index.less';

// Extend HTMLVideoElement interface to include webkit methods
interface ExtendedHTMLVideoElement extends HTMLVideoElement {
  webkitEnterFullscreen?: () => void;
  webkitRequestFullscreen?: () => void;
  mozRequestFullScreen?: () => void;
  msRequestFullscreen?: () => void;
}

interface VideoInfo {
  uuid: string;
  videoUrl: string;
  imageUrl: string;
  gifUrl?: string; // Add optional gifUrl field
  positivePrompt: string;
  negativePrompt: string;
  quality: string;
  duration: number;
  userRating?: string;
  videoFinishTime?: string;
  requestId?: string;
  createdAt?: string; // Video creation timestamp for progress calculation
  status: 'pending' | 'processing' | 'finish' | 'failed';
}

interface VideoItemProps {
  videoInfo: VideoInfo;
  onRegenerate?: (videoInfo: VideoInfo) => void;
  showPromptHeader?: boolean; // Whether to show the prompt header
  className?: string;
  progress?: number; // Video generation progress (0-100)
  loadVideoList: () => void; // Load video list
  onOptimizedVideoUpdate?: (regenerateResult: any, originalUuid: string) => Promise<void>;
}

const VideoItem: React.FC<VideoItemProps> = ({
  videoInfo,
  onRegenerate,
  showPromptHeader = true,
  className = '',
  progress = 0,
  loadVideoList,
  onOptimizedVideoUpdate,
}) => {
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const [isConverting, setIsConverting] = useState(false);
  const [gifProgress, setGifProgress] = useState(0);
  const [isLiked, setIsLiked] = useState(videoInfo.userRating === '1');
  const [isDisliked, setIsDisliked] = useState(videoInfo.userRating === '-1');
  const [isPromptExpanded, setIsPromptExpanded] = useState(false);
  const [isRegenerating, setIsRegenerating] = useState(false);
  const videoRef = useRef<HTMLVideoElement>(null);

  // Check if current device is mobile
  const isMobile = isMobileDevice();

  // Linear progress component for video generation
  const LinearProgress: React.FC<{progress: number}> = ({ progress: progressValue }) => {
    return (
      <div className="linear-progress-container">
        <div className="progress-title">{i18next.t('j-dingtalk-web_pages_aiVideo_components_VideoItem_Generating')}&nbsp;{Math.round(progressValue)}%</div>
        <div className="linear-progress">
          <div className="progress-track">
            <div
              className="progress-fill"
              style={{ width: `${progressValue}%` }}
            />

          </div>
        </div>
        <div className="progress-tip">{i18next.t('j-dingtalk-web_pages_aiVideo_components_VideoItem_ItIsExpectedToBe')}</div>
      </div>);
  };

  // Format time for video player
  const formatTime = (time: number): string => {
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    // Always pad minutes to 2 digits for consistent width
    return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
  };

  // Convert quality value to Chinese text
  const getQualityText = (quality: string): string => {
    switch (quality.toLowerCase()) {
      case 'hd':
      case 'high':
      case i18next.t('j-dingtalk-web_pages_aiVideo_components_VideoItem_Hd'):
        return i18next.t('j-dingtalk-web_pages_aiVideo_components_VideoItem_Hd');
      case 'sd':
      case 'standard':
      case i18next.t('j-dingtalk-web_pages_aiVideo_components_VideoItem_StandardClear'):
        return i18next.t('j-dingtalk-web_pages_aiVideo_components_VideoItem_StandardClear');
      default:
        return quality; // Return original value if no match
    }
  };

  // Handle video play/pause
  const handlePlayPause = () => {
    if (videoRef.current) {
      if (isPlaying) {
        videoRef.current.pause();
      } else {
        videoRef.current.play();
      }
      // Manually sync currentTime to ensure immediate update
      setCurrentTime(videoRef.current.currentTime);
    }
  };

  // Handle fullscreen
  const handleFullscreen = () => {
    if (videoRef.current) {
      const video = videoRef.current as ExtendedHTMLVideoElement;

      if (isMobile) {
        // For mobile devices, use webkitEnterFullscreen for iOS Safari
        if (video.webkitEnterFullscreen) {
          video.webkitEnterFullscreen();
        } else if (video.requestFullscreen) {
          video.requestFullscreen();
        } else {
          // Fallback: try to trigger native video controls
          video.controls = true;
          video.play();
          Toast.info({
            content: i18next.t('j-dingtalk-web_pages_aiVideo_components_VideoItem_PleaseClickTheFullScreen'),
            duration: 2,
            position: 'top',
            maskClickable: true,
          });
        }
      } else if (video.requestFullscreen) {
        video.requestFullscreen();
      } else if (video.webkitRequestFullscreen) {
        video.webkitRequestFullscreen();
      } else if (video.mozRequestFullScreen) {
        video.mozRequestFullScreen();
      } else if (video.msRequestFullscreen) {
        video.msRequestFullscreen();
      }
    }
  };

  // Handle video time update
  const handleTimeUpdate = () => {
    if (videoRef.current) {
      setCurrentTime(videoRef.current.currentTime);
    }
  };

  // Handle video metadata loaded
  const handleLoadedMetadata = () => {
    if (videoRef.current) {
      setDuration(videoRef.current.duration);
    }
  };

  // Handle like action
  const handleLike = async () => {
    try {
      if (isLiked) {
        // Cancel like if already liked
        await rateVideo({ uuid: videoInfo.uuid, rating: 0 });
        setIsLiked(false);
        Toast.success({
          content: i18next.t('j-dingtalk-web_pages_aiVideo_components_VideoItem_ThePraiseIsCanceled'),
          duration: 2,
          position: 'top',
          maskClickable: true,
        });
      } else {
        // Like the video (and cancel dislike if exists)
        await rateVideo({ uuid: videoInfo.uuid, rating: 1 });
        setIsLiked(true);
        setIsDisliked(false);
        Toast.success({
          content: i18next.t('j-dingtalk-web_pages_aiVideo_components_VideoItem_ThumbsUpSuccessfully'),
          duration: 2,
          position: 'top',
          maskClickable: true,
        });
      }
    } catch (error) {
      // 如果 rateVideo 返回 success: false, 则需要显示 errorMsg 信息
      Toast.fail({
        content: i18next.t('j-dingtalk-web_pages_aiVideo_components_VideoItem_TheOperationFailedPleaseTry'),
        duration: 3,
        position: 'top',
        maskClickable: true,
      });
    }
  };

  // Handle dislike action
  const handleDislike = async () => {
    try {
      if (isDisliked) {
        // Cancel dislike if already disliked
        await rateVideo({ uuid: videoInfo.uuid, rating: 0 });
        setIsDisliked(false);
        Toast.success({
          content: i18next.t('j-dingtalk-web_pages_aiVideo_components_VideoItem_TheCancellationPointIsStepped'),
          duration: 2,
          position: 'top',
          maskClickable: true,
        });
      } else {
        // Dislike the video (and cancel like if exists)
        await rateVideo({ uuid: videoInfo.uuid, rating: -1 });
        setIsDisliked(true);
        setIsLiked(false);
        Toast.success({
          content: i18next.t('j-dingtalk-web_pages_aiVideo_components_VideoItem_ClickSuccessfully'),
          duration: 2,
          position: 'top',
          maskClickable: true,
        });
      }
    } catch (error) {
      // 如果 rateVideo 返回 success: false, 则需要显示 errorMsg 信息
      Toast.fail({
        content: i18next.t('j-dingtalk-web_pages_aiVideo_components_VideoItem_TheOperationFailedPleaseTry'),
        duration: 3,
        position: 'top',
        maskClickable: true,
      });
    }
  };

  // Handle download
  const handleDownload = () => {
    if (videoInfo.videoUrl) {
      downloadVideo(videoInfo.videoUrl, videoInfo.uuid);
    }
  };

  // Handle convert to GIF with polling
  const handleConvertToGif = async () => {
    if (isConverting) return;

    // Check if GIF already exists in videoInfo
    if (videoInfo.gifUrl) {
      Toast.success({
        content: i18next.t('j-dingtalk-web_pages_aiVideo_components_VideoItem_GifAlreadyExistsStartDownloading'),
        duration: 2,
        position: 'top',
        maskClickable: true,
      });
      downloadGif(videoInfo.gifUrl, videoInfo.uuid);
      return;
    }

    setIsConverting(true);
    setGifProgress(0);

    try {
      // Step 1: Start GIF generation
      const generateResult = await generateGif({ uuid: videoInfo.uuid });

      // Check if generation is already completed
      if (generateResult.status === 'completed' && generateResult.gifUrl) {
        setGifProgress(100);
        downloadGif(generateResult.gifUrl, videoInfo.uuid);
        Toast.success({
          content: i18next.t('j-dingtalk-web_pages_aiVideo_components_VideoItem_GifGenerationCompleted'),
          duration: 2,
          position: 'top',
          maskClickable: true,
        });

        // Update video list to reflect the new gifUrl
        loadVideoList();
        return;
      }

      if (generateResult.status !== 'processing') {
        throw new Error(i18next.t('j-dingtalk-web_pages_aiVideo_components_VideoItem_GifGenerationFailedToStart'));
      }

      Toast.success({
        content: i18next.t('j-dingtalk-web_pages_aiVideo_components_VideoItem_GifGenerationHasStartedPlease'),
        duration: 2,
        position: 'top',
        maskClickable: true,
      });

      // Step 2: Start polling for GIF status
      await pollGifStatus();
    } catch (error) {
      Toast.fail({
        content: i18next.t('j-dingtalk-web_pages_aiVideo_components_VideoItem_GifGenerationFailedPleaseTry'),
        duration: 3,
        position: 'top',
        maskClickable: true,
      });
      setIsConverting(false);
      setGifProgress(0);
    }
  };

  // Poll GIF generation status
  const pollGifStatus = async () => {
    let pollCount = 0;
    const maxPollCount = 50; // Maximum 50 polls (2.5 minutes total)
    const pollInterval = 3000; // 3 seconds interval

    const poll = async (): Promise<void> => {
      try {
        pollCount++;

        // Update progress based on poll count (rough estimation)
        const progressPercentage = Math.min(pollCount / maxPollCount * 90, 90);
        setGifProgress(progressPercentage);

        const result = await checkGifStatus({ uuid: videoInfo.uuid });

        // Check if status is completed with gifUrl
        if (result.status === 'completed' && result.gifUrl) {
          setGifProgress(100);

          // Auto download the GIF
          downloadGif(result.gifUrl, videoInfo.uuid);

          Toast.success({
            content: i18next.t('j-dingtalk-web_pages_aiVideo_components_VideoItem_GifGenerationCompleted'),
            duration: 2,
            position: 'top',
            maskClickable: true,
          });
          setIsConverting(false);
          setGifProgress(0);

          // Update video list to reflect the new gifUrl
          loadVideoList();
          return;
        }

        // Check if status is failed
        if (result.status === 'failed') {
          throw new Error(i18next.t('j-dingtalk-web_pages_aiVideo_components_VideoItem_GifGenerationFailed'));
        }

        // If status is processing, continue polling
        if (result.status === 'processing') {
          // Check if we've reached the maximum poll count
          if (pollCount >= maxPollCount) {
            throw new Error(i18next.t('j-dingtalk-web_pages_aiVideo_components_VideoItem_GifGenerationTimeout'));
          }

          // Continue polling after 10 seconds
          setTimeout(() => {
            poll();
          }, pollInterval);
          return;
        }

        // Handle unexpected status
        throw new Error(i18next.t('j-dingtalk-web_pages_aiVideo_components_VideoItem_UnknownStatusResultstatus', { resultStatus: result.status }));
      } catch (error) {
        Toast.fail({
          content: error instanceof Error ? error.message : i18next.t('j-dingtalk-web_pages_aiVideo_components_VideoItem_GifGenerationFailedPleaseTry'),
          duration: 3,
          position: 'top',
          maskClickable: true,
        });
        setIsConverting(false);
        setGifProgress(0);
      }
    };

    // Start the first poll
    poll();
  };

  // Optimized video update handler after regeneration
  const handleOptimizedVideoUpdate = async (regenerateResult: any, originalUuid: string) => {
    // Use the optimized update function from parent if available, otherwise fallback
    if (onOptimizedVideoUpdate) {
      await onOptimizedVideoUpdate(regenerateResult, originalUuid);
    } else {
      // Fallback to original behavior
      loadVideoList();
    }
  };

  // Handle regenerate - directly call regenerate API for failed videos
  const handleRegenerate = async () => {
    // For failed videos, directly regenerate without going to form
    if (videoInfo.status === 'failed') {
      if (isRegenerating) return;

      setIsRegenerating(true);

      try {
        Toast.info({
          content: i18next.t('j-dingtalk-web_pages_aiVideo_components_VideoItem_RegeneratingVideo'),
          duration: 2,
          position: 'top',
          maskClickable: true,
        });

        // Call regenerate API with the original video's UUID
        const result = await reGenerateVideo({
          fromUuid: videoInfo.uuid,
        });

        if (result?.success) {
          Toast.success({
            content: i18next.t('j-dingtalk-web_pages_aiVideo_components_VideoItem_RegenerationHasStartedPleaseWait'),
            duration: 2,
            position: 'top',
            maskClickable: true,
          });

          // Optimized: Use targeted status check instead of full list reload
          await handleOptimizedVideoUpdate(result, videoInfo.uuid);
        } else {
          throw new Error(result?.errorMsg || i18next.t('j-dingtalk-web_pages_aiVideo_components_VideoItem_RegenerationFailed'));
        }
      } catch (error) {
        // 如果 reGenerateVideo 返回 success: false, 则需要显示 errorMsg 信息
        Toast.fail({
          content: error instanceof Error ? error?.message : i18next.t('j-dingtalk-web_pages_aiVideo_components_VideoItem_RegenerationFailedPleaseTryAgain'),
          duration: 3,
          position: 'top',
          maskClickable: true,
        });
      } finally {
        setIsRegenerating(false);
      }
      return;
    }

    // For other cases, use the original callback behavior
    if (onRegenerate) {
      onRegenerate(videoInfo);
    }
  };

  // Handle prompt toggle
  const handlePromptToggle = () => {
    setIsPromptExpanded(!isPromptExpanded);
  };

  // Handle remove
  const handleRemove = async () => {
    if (!videoInfo.uuid) {
      return;
    }

    // 删除操作之前需要先弹框确认，使用 dingtalk-design-mobile 的 Modal
    Modal.alert(i18next.t('j-dingtalk-web_pages_aiVideo_components_VideoItem_ConfirmDeletion'), i18next.t('j-dingtalk-web_pages_aiVideo_components_VideoItem_AreYouSureYouWant'), [
      {
        text: i18next.t('j-dingtalk-web_pages_aiVideo_components_VideoItem_Cancel'),
        onClick: () => {

        // User cancelled deletion - no action needed
        } },
      {
        text: i18next.t('j-dingtalk-web_pages_aiVideo_components_VideoItem_Delete'),
        style: { color: '#FF0E53' },
        onClick: async () => {
          try {
            const result = await removeVideo({ uuid: videoInfo.uuid });
            if (result?.success) {
              Toast.success({
                content: i18next.t('j-dingtalk-web_pages_aiVideo_components_VideoItem_DeletedSuccessfully'),
                duration: 2,
                position: 'top',
                maskClickable: true,
              });
              // Delete successfully, refresh video list
              loadVideoList();
            } else {
              // Handle API response with success: false
              Toast.fail({
                content: result?.errorMsg || i18next.t('j-dingtalk-web_pages_aiVideo_components_VideoItem_FailedToDeletePleaseTry'),
                duration: 3,
                position: 'top',
                maskClickable: true,
              });
            }
          } catch (error) {
            Toast.fail({
              content: i18next.t('j-dingtalk-web_pages_aiVideo_components_VideoItem_FailedToDeletePleaseTry'),
              duration: 3,
              position: 'top',
              maskClickable: true,
            });
          }
        },
      }]);
  };

  // Add event listeners for video
  useEffect(() => {
    const video = videoRef.current;
    if (video) {
      video.addEventListener('timeupdate', handleTimeUpdate);
      video.addEventListener('loadedmetadata', handleLoadedMetadata);

      return () => {
        video.removeEventListener('timeupdate', handleTimeUpdate);
        video.removeEventListener('loadedmetadata', handleLoadedMetadata);
      };
    }
  }, []);

  return (
    <div className={`video-item ${className}`}>
      {/* Prompt Header - optional */}
      {showPromptHeader &&
      <div className="video-item-header">
        <div className="prompt-info-container">
          <div className={`prompt-info ${isPromptExpanded ? 'expanded' : 'collapsed'}`}>
            {getQualityText(videoInfo.quality)} / {videoInfo.duration}s /
            {videoInfo.positivePrompt}
          </div>
          <div
            className={`prompt-toggle-icon ${isPromptExpanded ? 'expanded' : ''}`}
            onClick={handlePromptToggle}
          >
            <DownArrowOutlined />
          </div>
        </div>
      </div>
      }

      {/* Video Content */}
      <div className="video-content">
        {videoInfo.status === 'finish' && videoInfo.videoUrl ?
          <div className="video-container">
            <video
              ref={videoRef}
              src={videoInfo.videoUrl}
              className="video-player"
              poster={videoInfo.imageUrl}
              preload="metadata"
              playsInline // Enable inline play for better fullscreen support
              controls={false} // Use custom controls
              {...{
                'webkit-playsinline': 'true', // Safari compatibility
                'x5-video-player-type': 'h5', // Tencent X5 browser compatibility
                'x5-video-player-fullscreen': 'true', // Enable fullscreen for X5
              }}
              onPlay={() => {
                setIsPlaying(true);
                // Sync currentTime when play starts
                if (videoRef.current) {
                  setCurrentTime(videoRef.current.currentTime);
                }
              }}
              onPause={() => {
                setIsPlaying(false);
                // Sync currentTime when paused
                if (videoRef.current) {
                  setCurrentTime(videoRef.current.currentTime);
                }
              }}
            />

            <div className="video-controls">
              <div className="control-left">
                <button className="play-button" onClick={handlePlayPause}>
                  {isPlaying ? <PlayOutlined /> : <PauseFilled />}
                </button>
                <span className="time-display">
                  {formatTime(currentTime)}/{formatTime(duration || +videoInfo.duration)}
                </span>
              </div>
              <div className="control-right">
                <button className="fullscreen-button" onClick={handleFullscreen}>
                  <PlayerFullscreenLOutlined />
                </button>
              </div>
            </div>
          </div> :

          <div className="video-placeholder">
            <img src={videoInfo.imageUrl} alt="Video thumbnail" className="thumbnail-image" />
            <div className="status-overlay">
              {videoInfo.status === 'pending' &&
              <div className="status-text">{i18next.t('j-dingtalk-web_pages_aiVideo_components_VideoItem_WaitingForGeneration')}</div>
              }
              {videoInfo.status === 'processing' &&
              <div className="status-processing">
                <LinearProgress progress={progress} />
              </div>
              }
              {videoInfo.status === 'failed' &&
              <div className="status-text">{i18next.t('j-dingtalk-web_pages_aiVideo_components_VideoItem_FailedToGenerate')}</div>
              }
            </div>
          </div>
        }
      </div>

      {/* Action Buttons and Completion Time - show when video is generated or failed */}
      {((videoInfo.status === 'finish' && videoInfo.videoUrl) || videoInfo.status === 'failed') &&
      <>
        {/* PC Layout: Action buttons and completion time in same row */}
        {!isMobile &&
          <div className="action-section pc-layout">
            <div className="action-buttons">
              {videoInfo.status === 'finish' &&
              <>
                <button className={`action-button ${isLiked ? 'liked' : ''}`} onClick={handleLike}>
                  {isLiked ? <LikeFilled /> : <LikeOutlined />}
                </button>
                <button className={`action-button ${isDisliked ? 'disliked' : ''}`} onClick={handleDislike}>
                  {isDisliked ? <DislikeFilled /> : <DislikeOutlined />}
                </button>
                {onRegenerate &&
                <button className="action-button" onClick={handleRegenerate}>
                  <RefreshOutlined />
                </button>
                }
                <button className="action-button" onClick={handleDownload}>
                  <DownloadAndSaveOutlined />
                </button>
                <button
                  className={`action-button gif-button ${isConverting ? 'loading' : ''}`}
                  onClick={handleConvertToGif}
                  disabled={isConverting}
                >
                  {(() => {
                    if (videoInfo.gifUrl) {
                      return i18next.t('j-dingtalk-web_pages_aiVideo_components_VideoItem_DownloadGif');
                    }
                    if (isConverting) {
                      return `${i18next.t('j-dingtalk-web_pages_aiVideo_components_VideoItem_ConvertingToGif')} ${Math.round(gifProgress)}%`;
                    }
                    return i18next.t('j-dingtalk-web_pages_aiVideo_components_VideoItem_ConvertingToGif');
                  })()}
                </button>
              </>
              }
              {videoInfo.status === 'failed' &&
              <>
                <button
                  className={`action-button ${isRegenerating ? 'loading' : ''}`}
                  onClick={handleRegenerate}
                  disabled={isRegenerating}
                >

                  <RefreshOutlined />
                </button>
              </>
              }
              <button className="action-button" onClick={handleRemove}>
                <DeleteOutlined />
              </button>
            </div>
            {
              videoInfo.videoFinishTime && videoInfo.status === 'finish' &&
              <div className="completion-time">
                {formatCompletionTime(new Date(videoInfo.videoFinishTime))}
              </div>

            }
          </div>
        }

        {/* Mobile Layout: Action buttons and completion time in separate rows */}
        {isMobile &&
          <>
            <div className="action-buttons mobile-layout">
              {videoInfo.status === 'finish' &&
              <>
                <button className={`action-button ${isLiked ? 'liked' : ''}`} onClick={handleLike}>
                  {isLiked ? <LikeFilled /> : <LikeOutlined />}
                </button>
                <button className={`action-button ${isDisliked ? 'disliked' : ''}`} onClick={handleDislike}>
                  {isDisliked ? <DislikeFilled /> : <DislikeOutlined />}
                </button>
                {onRegenerate &&
                <button className="action-button" onClick={handleRegenerate}>
                  <RefreshOutlined />
                </button>
                }
                <button className="action-button" onClick={handleDownload}>
                  <DownloadAndSaveOutlined />
                </button>
                <button
                  className={`action-button gif-button ${isConverting ? 'loading' : ''}`}
                  onClick={handleConvertToGif}
                  disabled={isConverting}
                >

                  {(() => {
                    if (videoInfo.gifUrl) {
                      return i18next.t('j-dingtalk-web_pages_aiVideo_components_VideoItem_DownloadGif');
                    }
                    if (isConverting) {
                      return `${i18next.t('j-dingtalk-web_pages_aiVideo_components_VideoItem_ConvertingToGif')} ${Math.round(gifProgress)}%`;
                    }
                    return i18next.t('j-dingtalk-web_pages_aiVideo_components_VideoItem_ConvertingToGif');
                  })()}
                </button>
              </>
              }
              {videoInfo.status === 'failed' &&
              <>
                <button
                  className={`action-button ${isRegenerating ? 'loading' : ''}`}
                  onClick={handleRegenerate}
                  disabled={isRegenerating}
                >

                  <RefreshOutlined />
                </button>
              </>
              }
              <button className="action-button" onClick={handleRemove}>
                <DeleteOutlined />
              </button>
            </div>
            {
              videoInfo.videoFinishTime && videoInfo.status === 'finish' &&
              <div className="completion-time mobile-layout">
                {formatCompletionTime(new Date(videoInfo.videoFinishTime))}
              </div>

            }
          </>
        }
      </>
      }
    </div>
  );
};

export default VideoItem;
