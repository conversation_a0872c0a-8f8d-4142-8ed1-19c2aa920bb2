.top-merchant {
  min-height: 100vh;

  // Header Section
  .merchant-header {
    padding: 16px;

    .merchant-info {
      display: flex;
      align-items: flex-start;
      gap: 12px;

      .merchant-avatar {
        position: relative;
        font-size: 0;

        .merchant-avatar-image {
          width: 52px;
          height: 52px;
          border-radius: 8px;
          overflow: hidden;

          img {
            border-radius: 8px;
          }
        }

        .merchant-badge-image {
          position: absolute;
          top: 0;
          left: 0;
          width: 18px;
          height: 24px;
        }
      }

      .merchant-details {
        flex: 1;

        .merchant-title {
          display: flex;
          align-items: center;
          justify-content: flex-start;
          margin-bottom: 8px;
        }

        .merchant-name {
          font-size: 16px;
          font-weight: 500;
          color: var(--common_level1_base_color);
          max-width: 260px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          line-height: 24px;
          margin-right: 4px;
        }

        .merchant-tag {
          display: inline-block;
          color: var(--common_level2_base_color);
          font-size: 10px;
          line-height: 12px;
          padding: 3px 6px;
          border-radius: 4px;
          border: 0.5px solid var(--common_line_hard_color);
          margin-right: 4px;
        }

        .merchant-stats {
          display: flex;
          gap: 16px;

          .fans-count {
            color: var(--common_level2_base_color);
            font-size: 12px;
            font-weight: 400;
            line-height: 18px;
          }

          .fans-num {
            color: #ff0e53;
            font-weight: 500;
            font-size: 16px;
          }
        }
      }
    }
  }

  // Section Titles
  .section-title {
    font-size: 16px;
    font-weight: 500;
    line-height: 22px;
    color: var(--common_level1_base_color);
    margin: 0 16px 12px 16px;
  }

  .interaction-header {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    gap: 10px;
    margin: 0 16px 12px 16px;
    &.none {
      display: none;
    }

    .interaction-label {
      font-size: 12px;
      color: var(--common_level2_base_color);
      line-height: 18px;
      padding: 6px 8px;
      border-radius: 6px;
      background-color: var(--common_overlay_area_color);
      cursor: pointer;
      transition: all 0.2s ease;

      &:hover {
        background: rgba(132, 139, 153, 0.12);
      }

      &.active {
        color: var(--common_level1_base_color);
        font-weight: 500;
        background: rgba(132, 139, 153, 0.16);
      }
    }
  }

  // Data Overview
  .data-overview {
    padding: 16px 0;

    &.none {
      display: none;
    }

    .data-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 8px;
      padding: 0 16px;

      .data-item {
        border-radius: 8px;
        background-color: var(--common_bg_z0_color);
        text-align: center;
        padding: 12px;

        .data-label {
          font-size: 12px;
          color: var(--common_level2_base_color);
          line-height: 18px;
          margin-bottom: 4px;
        }

        .data-value {
          font-size: 20px;
          font-weight: 500;
          line-height: 28px;
          color: #ff0e53;
        }
      }
    }
  }

  // Style Overview
  .style-overview {
    padding: 16px 0;

    .style-notes-list {
      display: none;
      flex-direction: column;
      gap: 16px;
      margin: 0 16px;

      &.active {
        display: flex;
      }

      .style-note-item {
        display: flex;
        align-items: flex-start;
        flex-direction: row;
        gap: 8px;
        overflow: hidden;
        cursor: pointer;

        .style-note-image-container {
          position: relative;
          font-size: 0;
          cursor: pointer;
          .style-note-image {
            width: 164px;
            height: 212px;
            flex-shrink: 0;
            border-radius: 8px;
            overflow: hidden;
          }
          .play-icon {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
          }
        }

        .style-note-info {
          flex: 1;
          min-width: 0; // 关键：允许flex项目收缩到内容宽度以下
          display: flex;
          flex-direction: column;
          justify-content: space-between;

          .style-note-description {
            font-size: 14px;
            font-weight: 500;
            color: var(--common_level1_base_color);
            line-height: 22px;
            height: 44px;
            margin-bottom: 8px;
            overflow: hidden;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
          }

          .style-note-likes {
            display: flex;
            align-items: center;
            gap: 4px;
            margin-bottom: 14px;

            .like-icon {
              width: 16px;
              height: 16px;
            }

            .likes-count {
              font-size: 14px;
              color: var(--common_level1_base_color);
              line-height: 22px;
            }
          }

          .style-note-desc {
            font-size: 12px;
            color: var(--common_level2_base_color);
            line-height: 18px;
            margin-bottom: 8px;
          }

          .style-note-products {
            display: flex;
            flex-wrap: nowrap;
            flex-direction: row;
            gap: 4px;
            overflow-x: auto;
            overflow-y: hidden;
            -webkit-overflow-scrolling: touch;
            scroll-behavior: smooth;
            // 确保Mac触摸板滑动生效
            overscroll-behavior-x: contain;
            // PC端显示细滚动条，移动端自动隐藏
            &::-webkit-scrollbar {
              height: 6px;
              background: transparent;
            }
            &::-webkit-scrollbar-thumb {
              background: rgba(0,0,0,0.12);
              border-radius: 3px;
              &:hover {
                background: rgba(0,0,0,0.2);
              }
            }
            &::-webkit-scrollbar-track {
              background: transparent;
            }
            .style-note-product {
              position: relative;
              font-size: 0;
              width: 98px;
              height: 98px;
              flex-shrink: 0; // 防止被压缩
              min-width: 98px; // 确保最小宽度
  
              .style-note-product-image {
                width: 100%;
                height: 100%;
                border-radius: 8px;
              }
  
              .product-search-overlay {
                position: absolute;
                bottom: 4px;
                right: 4px;
                display: flex;
                align-items: center;
                gap: 4px;
                border-radius: 190px;
                padding: 4px 8px;
                background: rgba(0, 0, 0, 0.6);
                backdrop-filter: blur(10px);
  
                .search-icon {
                  font-size: 16px;
                  color: #ffffff;
                }
  
                .search-text {
                  font-size: 12px;
                  color: #ffffff;
                  line-height: 16px;
                }
              }
            }
          }
        }
      }
    }

    // Keep old styles for backward compatibility (hidden by default)
    .style-content {
      display: flex;
      gap: 8px;
      border-radius: 8px;
      margin: 0 16px;
      flex-direction: column;
      background: var(--common_bg_z0_color);
      overflow: hidden;

      .style-item {
        display: flex;
        align-items: center;
        padding: 8px 12px 12px 8px;
        gap: 8px;
      }

      .style-image {
        width: 112px;
        height: 84px;
        flex-shrink: 0;
      }

      .style-info {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: space-between;

        .style-description {
          font-size: 14px;
          color: var(--common_level1_base_color);
          line-height: 22px;
          margin: 0;
          font-weight: 500;
          overflow: hidden;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
        }

        .style-likes {
          display: flex;
          align-items: center;
          gap: 5px;
          margin-top: 18px;

          .like-icon {
            width: 16px;
            height: 16px;
          }

          .likes-count {
            font-size: 14px;
            color: var(--common_level1_base_color);
            line-height: 22px;
          }
        }
      }

      .style-desc {
        font-size: 12px;
        color: var(--common_level2_base_color);
        line-height: 18px;
        padding: 0 8px;
      }

      .style-products {
        display: flex;
        flex-wrap: nowrap;
        gap: 4px;
        overflow-x: auto;
        overflow-y: hidden;
        -webkit-overflow-scrolling: touch;
        scroll-behavior: smooth;
        // 确保Mac触摸板滑动生效
        overscroll-behavior-x: contain;
        // 添加padding确保滚动区域足够
        padding: 2px 0;
        // 隐藏滚动条的高度
        &::-webkit-scrollbar {
          height: 0;
        }
        .style-product-item {
          position: relative;
          font-size: 0;
          flex-shrink: 0; // 防止被压缩
          min-width: 88px; // 确保最小宽度
          .style-product-image {
            width: 88px;
            height: 88px;
          }
          .product-price-small {
            position: absolute;
            bottom: 8px;
            left: 8px;
            font-size: 12px;
            line-height: 14px;
            border-radius: 4px;
            padding: 2px 4px;
            color: #ffffff;
            background-color: rgba(17, 17, 17, 0.7);
          }
        }
      }
    }
  }

  // Merchant Products
  .merchant-products {
    padding: 16px 0;

    &.none {
      display: none;
    }

    .product-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;

      .sales-label {
        font-size: 13px;
        color: #333333;
        font-weight: 500;
      }

      .publish-status {
        font-size: 12px;
        color: #999999;
      }
    }

    .product-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 24px 16px;
      margin: 0 16px;

      .product-item {
        cursor: pointer;
        width: 100%;
        box-sizing: border-box;

        .product-image-container {
          position: relative;
          margin-bottom: 8px;
          font-size: 0;

          .product-image {
            height: 280px !important;
            font-size: 0;
            border-radius: 8px;
            overflow: hidden;
            object-fit: cover;
          }

          .product-actions {
            position: absolute;
            bottom: 8px;
            left: 8px;
            right: 8px;
            display: flex;
            justify-content: flex-end;
            align-items: center;

            .similar-button {
              background-color: rgba(0, 0, 0, 0.6);
              border: none;
              border-radius: 16px;
              color: #ffffff;
              font-size: 12px;
              padding: 6px 12px;
              cursor: pointer;
              display: flex;
              align-items: center;
              gap: 4px;
              .similar-icon {
                font-size: 18px;
              }
            }
          }
        }

        .product-info {
          .product-title {
            font-size: 14px;
            font-weight: 500;
            color: var(--common_level1_base_color);
            line-height: 22px;
            height: 44px;
            margin-bottom: 8px;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
          }

          .product-price {
            font-size: 24px;
            color: #ff0e53;
            font-weight: 600;
            line-height: 24px;
            margin-bottom: 4px;
            span {
              font-size: 14px;
            }
          }

          .product-sales {
            display: inline-block;
            font-size: 10px;
            line-height: 12px;
            border-radius: 4px;
            padding: 2.5px 4px;
            margin-right: 4px;
            color: var(--common_level2_base_color);
            border: 0.5px solid var(--common_level4_base_color);
            &:last-child {
              margin-right: 0;
            }
          }
        }
      }
    }
  }

  // Loading more indicator styles
  .loading-more-container {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 20px;
    margin-top: 16px;

    .loading-more-text {
      font-size: 14px;
      color: var(--common_level2_base_color);
      display: flex;
      align-items: center;
      gap: 8px;

      &::after {
        content: '';
        width: 16px;
        height: 16px;
        border: 2px solid var(--common_level3_base_color);
        border-top: 2px solid var(--common_level1_base_color);
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }
    }
  }

  .no-more-data-container {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 20px;
    margin-top: 16px;

    .no-more-data-text {
      font-size: 14px;
      color: var(--common_level3_base_color);
    }
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

  // Load more button styles for style notes
  .load-more-button-container {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 20px;

    .load-more-button {
      min-width: 120px;
      height: 36px;
      border-radius: 18px;
      font-size: 14px;
      font-weight: 500;

      &.ant-btn-primary {
        background: var(--common_brand_base_color);
        border-color: var(--common_brand_base_color);

        &:hover {
          background: var(--common_brand_hover_color);
          border-color: var(--common_brand_hover_color);
        }

        &:active {
          background: var(--common_brand_active_color);
          border-color: var(--common_brand_active_color);
        }
      }
    }
  }
}
