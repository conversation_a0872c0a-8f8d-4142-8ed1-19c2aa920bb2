.j-commodity-comments {
  margin-top: 24px;
  margin-bottom: 24px;

  .comments-list {
    margin: 0 16px;

    .comments-rates {
      --star-size: 16px;
      --active-color: #FF0E53;
      margin-bottom: 8px;
    }

    .comments-item {
      display: flex;
      justify-content: flex-start;
      align-items: flex-start;
      flex-direction: column;
      margin-bottom: 24px;

      .comments-title {
        font-size: 15px;
        line-height: 22px;
        color: var(--common_level1_base_color);
        font-weight: 500;
      }

      .comments-content {
        font-size: 13px;
        line-height: normal;
        color: var(--common_level3_base_color);
        margin-top: 8px;
      }

      .comments-date {
        font-size: 12px;
        line-height: 18px;
        color: var(--common_level4_base_color);
        margin-top: 8px;
      }

      &:last-child {
        padding-bottom: 0;
      }
    }
  }

  // Custom scrollbar styles for better appearance
  &::-webkit-scrollbar {
    width: 4px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.1);
    border-radius: 2px;
    transition: background 0.3s ease;
  }

  &::-webkit-scrollbar-thumb:hover {
    background: rgba(0, 0, 0, 0.2);
  }

  // Dark mode scrollbar
  :root[data-dingtalk-theme='dark'] & {
    &::-webkit-scrollbar-thumb {
      background: rgba(255, 255, 255, 0.1);
    }

    &::-webkit-scrollbar-thumb:hover {
      background: rgba(255, 255, 255, 0.2);
    }
  }
}

.amazon-summary {
  margin: 24px 16px;
  &-title {
    font-size: 14px;
    font-weight: 500;
    line-height: 20px;
    color: var(--common_level1_base_color);
    margin-bottom: 4px;
  }
  &-desc {
    font-size: 12px;
    line-height: 18px;
    color: var(--common_level2_base_color);
    margin-bottom: 12px;
  }
  &-detail {
    font-size: 14px;
    line-height: 22px;
    color: var(--common_level1_base_color);
  }
}
