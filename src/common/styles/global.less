// 深色模式使用文档：https://yuque.antfin-inc.com/docs/share/8b63de41-dd76-4fac-8a4d-ba806873a565
@tailwind components;
@tailwind utilities;

* {
  margin: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  font-family: "PingFang SC", "Microsoft YaHei", Arial, sans-serif
}

:root {
  --custom_color: #fff;
}

:root[data-dingtalk-theme='dark'] {
  --custom_color: #111213;
}

// Global scrollbar styles for better appearance
* {
  // Custom scrollbar for webkit browsers
  &::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.1);
    border-radius: 3px;
    transition: all 0.3s ease;
  }

  &::-webkit-scrollbar-thumb:hover {
    background: rgba(0, 0, 0, 0.2);
  }

  &::-webkit-scrollbar-corner {
    background: transparent;
  }
}

// Dark mode scrollbar
:root[data-dingtalk-theme='dark'] {
  * {
    &::-webkit-scrollbar-thumb {
      background: rgba(255, 255, 255, 0.1);
    }

    &::-webkit-scrollbar-thumb:hover {
      background: rgba(255, 255, 255, 0.2);
    }
  }
}
